import os
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox
from preprocessing.ml_core import MODEL_REGISTRY

AUTO_MODE = False

def console_select(options, prompt, default=None, multiple=False):
    print(f"\n{prompt}")
    for i,opt in enumerate(options,1):
        print(f"  {i}. {opt}")
    if default:
        print(f"Default: {default}")
    if AUTO_MODE:
        return default if not multiple else (default if isinstance(default, list) else [default])
    while True:
        choice = input("Selection: ").strip()
        if not choice and default:
            return default if not multiple else (default if isinstance(default, list) else [default])
        if multiple and choice.lower()=='all':
            return options
        try:
            idxs = [int(x) for x in choice.split(',')]
            sel = [options[i-1] for i in idxs]
            return sel if multiple else sel[0]
        except:
            print("Invalid input.")

def select_las_files_dialog():
    """Open a file dialog to select multiple LAS files."""
    # Hide the main tkinter window
    root = tk.Tk()
    root.withdraw()

    # Open file dialog for multiple LAS files
    file_paths = filedialog.askopenfilenames(
        title="Select LAS Files",
        filetypes=[
            ("LAS files", "*.las"),
            ("All files", "*.*")
        ],
        multiple=True
    )

    root.destroy()

    if not file_paths:
        print("No files selected.")
        return []

    print(f"Selected {len(file_paths)} LAS files:")
    for i, path in enumerate(file_paths, 1):
        print(f"  {i}. {os.path.basename(path)}")

    return list(file_paths)

def get_io_paths():
    """Get input and output paths. Offers choice between directory or file selection."""
    print("\nChoose input method:")
    print("1. Select directory containing LAS files")
    print("2. Select individual LAS files (GUI dialog)")

    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            inp = input("Input LAS directory: ").strip()
            break
        elif choice == "2":
            print("Opening file selection dialog...")
            selected_files = select_las_files_dialog()
            if selected_files:
                inp = selected_files  # Return list of file paths instead of directory
                break
            else:
                print("No files selected. Please try again.")
                continue
        else:
            print("Invalid choice. Please enter 1 or 2.")

    out = input("Output directory: ").strip()
    return inp, out

def get_input_files():
    """Get input LAS files using file dialog."""
    print("Select LAS files using the file dialog...")
    selected_files = select_las_files_dialog()

    if not selected_files:
        print("No files selected. Exiting.")
        return None

    return selected_files

def select_output_directory():
    """Open a directory dialog to select output directory."""
    root = tk.Tk()
    root.withdraw()

    output_dir = filedialog.askdirectory(
        title="Select Output Directory"
    )

    root.destroy()

    if not output_dir:
        print("No output directory selected.")
        return None

    print(f"Selected output directory: {output_dir}")
    return output_dir

def get_io_paths_simple():
    """Simple version that directly opens file dialog for LAS selection."""
    print("Select LAS files using the file dialog...")
    selected_files = select_las_files_dialog()

    if not selected_files:
        print("No files selected. Exiting.")
        return None, None

    out = input("Output directory: ").strip()
    return selected_files, out

def configure_log_selection(logs):
    feats = console_select(logs, "Select feature logs (comma separated indexes)", multiple=True, default=logs[:4])
    tlist = [l for l in logs if l not in feats]
    tgt = console_select(tlist, "Select target log", default=tlist[0])
    return feats, tgt

def configure_well_separation(wells):
    mode = console_select(['mixed','separated'], "Training/prediction mode?", default='mixed')
    if mode=='mixed':
        return {'mode':'mixed','training_wells':wells,'prediction_wells':wells}
    tr = console_select(wells, "Training wells", multiple=True)
    pr = [w for w in wells if w not in tr]
    pr = console_select(pr, "Prediction wells", multiple=True, default=pr)
    return {'mode':'separated','training_wells':tr,'prediction_wells':pr}

def get_prediction_mode():
    """
    Configure prediction mode with enhanced cross-validation.

    Returns:
        int: Prediction mode (1 for imputation, 2 for full prediction)
    """
    print("\n📋 Available prediction modes:")
    print("  1. Mode 1: Fill missing values (imputation) - with enhanced cross-validation")
    print("  2. Mode 2: Full prediction - with enhanced cross-validation")
    print("Default: 1")

    mode = console_select(['1','2'], "Select prediction mode", default='1')

    # Convert to maintain backward compatibility
    mode_int = int(mode)
    if mode_int == 2:
        return 2
    return 1

# Deep learning pipeline configuration removed - keeping minimal model set


def configure_gpu_optimization():
    """
    Configure GPU-specific optimization settings based on hardware capabilities.

    Returns:
        Dict containing GPU optimization configuration
    """
    import torch

    gpu_config = {
        'mixed_precision_enabled': False,
        'gpu_preprocessing_enabled': False,
        'compute_capability': None,
        'optimization_strategy': 'cpu'
    }

    if not torch.cuda.is_available():
        print("💻 CUDA not available - using CPU optimizations")
        return gpu_config

    # Detect GPU compute capability
    try:
        capability = torch.cuda.get_device_capability()
        gpu_name = torch.cuda.get_device_name()
        gpu_config['compute_capability'] = f"{capability[0]}.{capability[1]}"

        print(f"\n[GPU] GPU Detected: {gpu_name}")
        print(f"   Compute Capability: {capability[0]}.{capability[1]}")

        if capability[0] >= 7:
            # Volta/Turing/Ampere - supports mixed precision
            gpu_config['mixed_precision_enabled'] = True
            gpu_config['gpu_preprocessing_enabled'] = True
            gpu_config['optimization_strategy'] = 'modern_gpu'
            print("   [SUCCESS] Mixed precision training supported")
            print("   [SUCCESS] GPU preprocessing enabled")
        elif capability[0] == 6:
            # Pascal - disable mixed precision, enable GPU preprocessing
            gpu_config['mixed_precision_enabled'] = False
            gpu_config['gpu_preprocessing_enabled'] = True
            gpu_config['optimization_strategy'] = 'pascal_gpu'
            print("   WARNING: Mixed precision disabled (Pascal GPU - better performance with FP32)")
            print("   [SUCCESS] GPU preprocessing enabled (FP32 optimized)")
        else:
            # Older GPUs - CPU optimizations only
            gpu_config['optimization_strategy'] = 'cpu'
            print("   WARNING: GPU too old for optimizations - using CPU path")

    except Exception as e:
        print(f"   WARNING: GPU detection failed: {e}")

    return gpu_config


def apply_data_sufficiency_optimizations(hparams, df, feature_cols, target_col):
    """
    Apply data sufficiency optimizations to hyperparameters based on actual data.

    Args:
        hparams: Dictionary of hyperparameters for all models
        df: Input dataframe for analysis
        feature_cols: List of feature columns
        target_col: Target column name

    Returns:
        Updated hyperparameters optimized for data sufficiency
    """
    optimized_hparams = hparams.copy()

    # Analyze data characteristics
    wells = df['WELL'].unique()
    all_features = feature_cols + [target_col]

    # Calculate well statistics
    well_sizes = []
    max_continuous_intervals = []

    for well in wells:
        well_df = df[df['WELL'] == well]
        well_sizes.append(len(well_df))

        # Find continuous intervals
        is_valid = well_df[all_features].notna().all(axis=1)
        if is_valid.any():
            # Find continuous segments
            valid_changes = np.diff(is_valid.astype(int))
            interval_edges = np.where(valid_changes != 0)[0] + 1

            intervals = []
            if is_valid.iloc[0]:
                intervals.append([0])

            for edge in interval_edges:
                if len(intervals) > 0 and len(intervals[-1]) == 1:
                    intervals[-1].append(edge)
                else:
                    intervals.append([edge])

            if len(intervals) > 0 and len(intervals[-1]) == 1:
                intervals[-1].append(len(well_df))

            interval_lengths = [end - start for start, end in intervals]
            max_continuous_intervals.append(max(interval_lengths) if interval_lengths else 0)
        else:
            max_continuous_intervals.append(0)

    # Calculate optimal sequence length
    if max_continuous_intervals:
        # Use 75th percentile of maximum continuous intervals, but cap at reasonable limits
        optimal_seq_len = int(np.percentile([x for x in max_continuous_intervals if x > 0], 75))
        optimal_seq_len = max(16, min(optimal_seq_len, 64))  # Between 16 and 64
    else:
        optimal_seq_len = 32  # Conservative default

    # Calculate data sufficiency metrics
    median_well_size = np.median(well_sizes) if well_sizes else 0
    small_wells_ratio = sum(1 for size in well_sizes if size < 50) / len(well_sizes) if well_sizes else 1

    print(f"\n📊 Data Sufficiency Analysis:")
    print(f"   • Total wells: {len(wells)}")
    print(f"   • Median well size: {median_well_size:.0f} rows")
    print(f"   • Small wells (< 50 rows): {small_wells_ratio:.1%}")
    print(f"   • Optimal sequence length: {optimal_seq_len}")

    # Apply optimizations to basic models only
    # SAITS, BRITS, Transformer, mRNN removed

    return optimized_hparams


def apply_gpu_optimizations_to_hparams(hparams, gpu_config):
    """
    Apply GPU-specific optimizations to hyperparameters.

    Args:
        hparams: Dictionary of hyperparameters for all models
        gpu_config: GPU configuration from configure_gpu_optimization()

    Returns:
        Updated hyperparameters with GPU optimizations applied
    """
    optimized_hparams = hparams.copy()

    # Apply optimizations to basic models only
    # SAITS, BRITS, Transformer, mRNN removed

    return optimized_hparams


def configure_hyperparameters():
    params = {}
    for k,m in MODEL_REGISTRY.items():
        params[k] = {**m['fixed_params']}
        for p,meta in m['hyperparameters'].items():
            params[k][p] = meta['default']
    return params


# Small dataset configuration removed - keeping minimal model set


# Data clipping configuration removed - keeping minimal model set


# Small dataset fallback removed - keeping minimal model set
