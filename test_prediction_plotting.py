#!/usr/bin/env python3
"""
Lightweight test to verify that prediction mode produces a predicted column
and that plotting functions render without error (saving figures instead of showing).
"""

import os
import pandas as pd
import numpy as np

import reporting as rpt


def run_test():
    # Create synthetic result dataframe with predicted and imputed columns
    wells = ['W1', 'W2']
    depth = np.arange(100, dtype=float)

    df_rows = []
    for w in wells:
        tgt = np.sin(depth / 10.0) * 50 + 80  # some synthetic curve
        pred = tgt + np.random.normal(0, 2.0, size=depth.shape)  # noisy prediction
        # Introduce NaNs to simulate missing target values
        tgt_missing = tgt.copy()
        tgt_missing[::10] = np.nan

        for i, md in enumerate(depth):
            df_rows.append({
                'WELL': w,
                'MD': md,
                'GR': tgt_missing[i],
                'GR_pred': pred[i],
                'GR_imputed': tgt_missing[i] if not np.isnan(tgt_missing[i]) else pred[i]
            })

    res_df = pd.DataFrame(df_rows)

    # Minimal model result metadata
    mres = {
        'target': 'GR',
        'evaluations': [{
            'model_name': 'XGBoost',
            'mae': 2.0,
            'r2': 0.85,
            'rmse': 2.5,
            'composite_score': 0.5
        }],
        'best_model_name': 'XGBoost'
    }

    # Config mimicking separated mode
    cfg = {
        'mode': 'separated',
        'prediction_wells': wells
    }

    # Ensure predicted column can be detected
    pred_col = rpt.find_prediction_column(res_df, 'GR', 'predicted')
    assert pred_col == 'GR_pred', f"Expected 'GR_pred', got {pred_col}"

    # Monkeypatch _show_or_save to save figures silently
    def _save_only(fig, name_prefix):
        out_dir = os.path.join(os.getcwd(), 'plots')
        os.makedirs(out_dir, exist_ok=True)
        fig.savefig(os.path.join(out_dir, f"{name_prefix}.png"), dpi=100)
        import matplotlib.pyplot as plt
        plt.close(fig)

    rpt._show_or_save = _save_only

    # Run plotting functions (should not raise)
    rpt.create_summary_plots(res_df, mres, cfg, model_name='XGBoost', show_error_bands=True)
    rpt.create_separate_comparison_plots(res_df, mres, cfg, model_name='XGBoost')
    rpt.create_crossplot_analysis(res_df, mres, cfg, model_name='XGBoost', color_by='well')

    print("[PASS] Prediction plotting test completed; figures saved to ./plots")


if __name__ == '__main__':
    run_test()
